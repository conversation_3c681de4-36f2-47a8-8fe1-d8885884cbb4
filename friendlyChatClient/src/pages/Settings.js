import React, { useContext, useEffect } from 'react';
import { Platform, StyleSheet, View, ToastAndroid, ScrollView, Alert } from 'react-native';
import { useIAP, useIsAdFree } from '../contexts/IAPContext';
import {
  Button,
  List,
  RadioButton,
  TextInput,
  Text,
  useTheme,
  Surface,
  Switch,
  Chip,
} from 'react-native-paper';
import { useTheme as useCustomTheme } from '../contexts/ThemeContext';
import SocketService from '../services/socketIoService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  BigHead,
} from 'react-native-bigheads';
import AvatarEditorModal from '../Components/Messages/AvatarEditorModal';
import BackgroundSection from '../Components/BackgroundSection';
import SettingsContext from '../contexts/SettingsContext';
import Tooltip from 'react-native-walkthrough-tooltip';
import { useRecoilState } from 'recoil';
import { TooltipState } from '../store/tooltip/atoms';
import { Stage } from '../store/tooltip/enum';
import { generateName } from '../services/nameGenerator';
import * as filter from 'leo-profanity';

function Settings({ navigation }) {
  const theme = useTheme();
  const {
    theme: customTheme,
    colorScheme,
    isDarkMode,
    isSystemTheme,
    switchColorScheme,
    toggleDarkMode,
    enableSystemTheme,
    getAvailableColorSchemes,
  } = useCustomTheme();
  const {
    adFree,
    adFreeForOneMonth,
    removeAdsProduct,
    adFreeOneMonthProduct,
    purchaseRemoveAds,
    purchaseAdFreeOneMonth,
    isLoading
  } = useIAP();
  const isAdFree = useIsAdFree();
  const context = useContext(SettingsContext);
  const [toolTipStage, setToolTipStage] = useRecoilState(TooltipState);
  const [name, onChangeName] = React.useState(context?.name || '');
  const [namePlaceHolder, onChangeNamePlaceHolder] = React.useState('');
  const [gender, onGenderChange] = React.useState(context?.gender || 'm');
  const [loading, setLoading] = React.useState(false);
  const [showAvatarEditor, setShowAvatarEditor] = React.useState(false);
  const [avatar, onAvatarChange] = React.useState(context?.avatar || {
    accessory: 'none',
    clothing: 'shirt',
    clothingColor: 'blue',
    eyebrows: 'raised',
    eyes: 'normal',
    facialHair: 'none',
    hair: 'short',
    hairColor: 'black',
    hat: 'none',
    mouth: 'grin',
    skinTone: 'light',
    bgShape: 'circle',
  });


  const refreshName = () => {
    console.log('refreshName');
    const name = generateName();
    onChangeName(name);
    console.log('refreshName', name);
  }

  useEffect(() => {
    if (!name) {
      refreshName();
    }
  }, []);

  const selectRandomKey = object => {
    return Object.keys(object)[
      Math.floor(Math.random() * Object.keys(object).length)
    ];
  };

  const saveSettings = async () => {
    if (!context?.setSettings) {
      Alert.alert('Error', 'Unable to save settings. Please try again later.');
      return;
    }

    try {
      setLoading(true);
      const settings = {
        name: filter.clean(name || namePlaceHolder || generateName()),
        gender: gender || 'm',
        avatar: avatar || {
          accessory: 'none',
          clothing: 'shirt',
          clothingColor: 'blue',
          eyebrows: 'raised',
          eyes: 'normal',
          facialHair: 'none',
          hair: 'short',
          hairColor: 'black',
          hat: 'none',
          mouth: 'grin',
          skinTone: 'light',
          bgShape: 'circle',
        },
      };

      await Promise.all([
        AsyncStorage.setItem('settings', JSON.stringify(settings)),
        new Promise(resolve => {
          SocketService.emit('saveSettings', settings);
          resolve();
        })
      ]);

      context.setSettings(settings);

      if (Platform.OS === 'android') {
        ToastAndroid.showWithGravity(
          'Settings Saved!',
          ToastAndroid.SHORT,
          ToastAndroid.CENTER,
        );
      } else {
        Alert.alert('Success', 'Settings Saved!');
      }

      navigation.goBack();
    } catch (error) {
      console.error('Failed to save settings:', error);
      Alert.alert('Error', 'Failed to save settings. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const updateAvatar = (key, value) => {
    onAvatarChange({ ...avatar, [key]: value });
  };

  const openAvatarEditor = () => {
    setShowAvatarEditor(true);
  };

  const closeAvatarEditor = () => {
    setShowAvatarEditor(false);
  };

  // Helper functions for premium theme restrictions
  const isPremiumTheme = (scheme) => {
    return scheme !== 'blue'; // Only blue is free
  };

  const handleThemeSelection = (scheme) => {
    if (isPremiumTheme(scheme) && !isAdFree) {
      Alert.alert(
        'Premium Feature',
        'Upgrade to Premium to unlock all themes and enjoy an ad-free experience!',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: TryPremium, onPress: purchaseOneMonth }
        ]
      );
      return;
    }
    switchColorScheme(scheme);
  };
  const purchaseOneMonth = async () => {
    const success = await purchaseAdFreeOneMonth();
    if (success) {
      Alert.alert('Success', 'Monthly Ad-Free activated! Enjoy ad-free experience for 30 days.');
    }
  }
  const purchaseSubscription = async () => {
    const success = await purchaseRemoveAds();
    if (success) {
      Alert.alert('Success', 'Ad-Free activated! Enjoy ad-free experience.');
    }
  }
  const TryPremium = `Try 1 Month Premium ${adFreeOneMonthProduct?.localizedPrice ?? ''}`

  return (
    <BackgroundSection>
      <ScrollView contentContainerStyle={styles.container}>
        <Surface style={styles.surface} elevation={2}>
          <TextInput
            mode="outlined"
            placeholder={namePlaceHolder}
            value={name}
            onChangeText={onChangeName}
            onFocus={() => onChangeNamePlaceHolder('Name')}
            clearTextOnFocus={true}
            style={styles.input}
            right={
              <TextInput.Icon
                icon="refresh"
                onPress={refreshName}
                forceTextInputFocus={false}
              />
            }
          />

          <RadioButton.Group onValueChange={onGenderChange} value={gender}>
            <View style={styles.radioGroup}>
              <View style={styles.radioItem}>
                <RadioButton value="m" />
                <Text variant="bodyMedium">Male</Text>
              </View>
              <View style={styles.radioItem}>
                <RadioButton value="f" />
                <Text variant="bodyMedium">Female</Text>
              </View>
            </View>
          </RadioButton.Group>

          <View style={styles.avatarContainer}>
            <BigHead
              bgShape="circle"
              {...avatar}
              hatColor="white"
              lipColor="pink"
              lashes={false}
              graphic="react"
              bgColor={theme.colors.primary}
              body={gender === 'm' ? 'chest' : 'breasts'}
              size={150}
            />
          </View>

          <Tooltip
            isVisible={toolTipStage === Stage.Avatar}
            content={<Text variant="bodyMedium">Click to customize your avatar</Text>}
            placement="top"
            useInteractionManager={true}
            onClose={() => setToolTipStage(Stage.End)}
            showChildInTooltip={false}>
            {avatar && (
              <Button
                mode="contained-tonal"
                icon="account-edit"
                onPress={openAvatarEditor}
                style={styles.button}>
                Customize Avatar
              </Button>
            )}
          </Tooltip>

          <AvatarEditorModal
            visible={showAvatarEditor}
            onDismiss={closeAvatarEditor}
            avatar={avatar}
            onAvatarChange={onAvatarChange}
            purchaseOneMonth={purchaseOneMonth}
            TryPremium={TryPremium}
            gender={gender}
          />

          {/* Theme & Appearance Section */}
          <List.Accordion
            theme={{ colors: { background: 'transparent' } }}
            style={styles.accordion}
            title="Theme & Appearance"
            left={props => <List.Icon {...props} icon="palette" />}>
            {/* Manual Dark Mode Toggle (only when system theme is off) */}
            {!isSystemTheme && (
              <View style={styles.themeOption}>
                <View style={styles.themeOptionContent}>
                  <Text variant="bodyLarge">Dark Mode</Text>
                  <Text variant="bodySmall" style={styles.themeOptionDescription}>
                    Use dark theme colors
                  </Text>
                </View>
                <Switch
                  value={isDarkMode}
                  onValueChange={toggleDarkMode}
                />
              </View>
            )}

            {/* Color Scheme Selection */}
            <View style={styles.colorSchemeSection}>
              <Text variant="bodyLarge" style={styles.colorSchemeTitle}>
                Color Scheme
              </Text>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.colorSchemeScrollContainer}
                style={styles.colorSchemeContainer}
              >
                {getAvailableColorSchemes().map((scheme) => (
                  <Chip
                    key={scheme}
                    selected={colorScheme === scheme}
                    onPress={() => handleThemeSelection(scheme)}
                    style={[
                      styles.colorSchemeChip,
                      colorScheme === scheme && styles.selectedColorSchemeChip,
                      isPremiumTheme(scheme) && !isAdFree && styles.premiumChip
                    ]}
                    textStyle={styles.colorSchemeChipText}
                    showSelectedOverlay={true}
                    icon={isPremiumTheme(scheme) && !isAdFree ? "lock" : undefined}
                  >
                    {scheme.charAt(0).toUpperCase() + scheme.slice(1)}
                  </Chip>
                ))}
              </ScrollView>
            </View>
          </List.Accordion>

          <View style={styles.iapButtonContainer}>
            <Button
              mode="contained"
              onPress={purchaseSubscription}
              loading={isLoading}
              disabled={isLoading || adFree}
              style={[styles.button, styles.iapButton]}
              contentStyle={styles.buttonContent}
              labelStyle={styles.buttonLabel}
              icon="star">
              {adFree ? 'Ad-Free Active' : `Upgrade to monthly Premium `}
            </Button>

            <Button
              mode="contained-tonal"
              onPress={purchaseOneMonth}
              loading={isLoading}
              disabled={isLoading || adFreeForOneMonth}
              style={[styles.button, styles.iapButton]}
              contentStyle={styles.buttonContent}
              labelStyle={styles.buttonLabel}
              icon="clock">
              {adFree ? 'Monthly Ad-Free Active' : TryPremium}
            </Button>
          </View>

          <View style={styles.buttonContainer}>
            <Button
              mode="contained"
              onPress={saveSettings}
              loading={loading}
              disabled={loading}
              style={styles.button}
              contentStyle={styles.buttonContent}
              labelStyle={styles.buttonLabel}>
              Save
            </Button>
            <Button
              mode="outlined"
              onPress={() => navigation.goBack()}
              disabled={loading}
              style={styles.button}
              contentStyle={styles.buttonContent}
              labelStyle={styles.buttonLabel}>
              Cancel
            </Button>
          </View>
        </Surface>
      </ScrollView>
    </BackgroundSection>
  );
}

const styles = StyleSheet.create({
  iapButtonContainer: {
    marginVertical: 20,
    gap: 12,
    paddingHorizontal: 4,
  },
  iapButton: {
    width: '100%',
    marginVertical: 4,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  container: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  surface: {
    padding: 24,
    borderRadius: 20,
    elevation: 4,
    shadowColor: '#000',
    gap: 12,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.12,
    shadowRadius: 8,
  },
  input: {
    borderRadius: 12,
  },
  radioGroup: {
    flexDirection: 'row',
    justifyContent: 'center',
    // marginBottom: 20,
    backgroundColor: 'rgba(0,0,0,0.03)',
    borderRadius: 16,
    // padding: 12,
  },
  radioItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 12,
  },
  accordion: {
    // marginVertical: 20,
    // backgroundColor: 'transparent',
    // borderRadius: 16,
    // overflow: 'hidden',
    // elevation: 1,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 1,
    // },
    // shadowOpacity: 0.05,
    // shadowRadius: 2,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 24,
    gap: 12,
  },
  button: {
    flex: 1,
    marginHorizontal: 4,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  buttonContent: {
    paddingVertical: 12,
  },
  buttonLabel: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: Platform.select({
      ios: 'Poppins-Regular',
      android: 'PoppinsRegular-B2Bw',
    }),
  },
  avatarContainer: {
    alignItems: 'center',
    // marginVertical: 24,
    backgroundColor: 'rgba(0,0,0,0.02)',
    borderRadius: 20,
    // padding: 20,
  },
  // Theme-related styles
  themeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginVertical: 4,
  },
  themeOptionContent: {
    flex: 1,
    marginRight: 16,
  },
  themeOptionDescription: {
    opacity: 0.7,
    marginTop: 2,
  },
  colorSchemeSection: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginVertical: 4,
  },
  colorSchemeTitle: {
    marginBottom: 12,
  },
  colorSchemeContainer: {
    marginTop: 8,
  },
  colorSchemeScrollContainer: {
    paddingHorizontal: 4,
    gap: 8,
  },
  colorSchemeChip: {
    marginRight: 8,
    minWidth: 80,
  },
  selectedColorSchemeChip: {
    backgroundColor: 'rgba(33, 150, 243, 0.12)',
  },
  colorSchemeChipText: {
    fontSize: 14,
    fontFamily: Platform.select({
      ios: 'Poppins-Regular',
      android: 'PoppinsRegular-B2Bw',
    }),
  },
  premiumChip: {
    opacity: 0.6,
  },
});

export default Settings;
