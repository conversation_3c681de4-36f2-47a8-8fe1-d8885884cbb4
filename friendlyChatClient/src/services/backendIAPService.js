import { Platform } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import Config from 'react-native-config';
const { backEndUrl, appName } = Config;

class BackendIAPService {
  constructor() {
    // You should set this to your backend URL
    this.baseURL = backEndUrl + '/api/iap';

    this.deviceId = null;
    this.channelId = null;
    this.initializeDeviceInfo();
  }

  async initializeDeviceInfo() {
    try {
      this.deviceId = await DeviceInfo.getUniqueId();
      this.channelId = appName;
      console.log('Backend IAP Service initialized with deviceId:', this.deviceId, this.channelId);
    } catch (error) {
      console.error('Error initializing device info:', error);
    }
  }

  /**
   * Verify a purchase with the backend
   * @param {Object} purchaseData - Purchase data from react-native-iap
   * @returns {Promise<Object>} Verification result
   */
  async verifyPurchase(purchaseData) {
    try {
      if (!this.deviceId || !this.channelId) {
        await this.initializeDeviceInfo();
      }

      const platform = Platform.OS;
      const requestBody = {
        platform: platform,
        productId: purchaseData.productId,
        receipt: purchaseData.transactionReceipt,
        deviceId: this.deviceId,
        channelId: this.channelId
      };

      // Add platform-specific data
      if (platform === 'android') {
        requestBody.purchaseToken = purchaseData.purchaseToken;
      }

      console.log('Verifying purchase with backend:', requestBody.productId);

      const response = await fetch(`${this.baseURL}/verify-purchase`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Purchase verification failed');
      }

      console.log('Backend verification successful:', result);
      return {
        success: true,
        purchase: result.purchase,
        userStatus: result.userStatus
      };

    } catch (error) {
      console.error('Backend purchase verification error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get user's current IAP status from backend
   * @returns {Promise<Object>} User's IAP status
   */
  async getUserStatus() {
    try {
      if (!this.deviceId || !this.channelId) {
        await this.initializeDeviceInfo();
      }

      const response = await fetch(`${this.baseURL}/status/${this.deviceId}/${this.channelId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to get user status');
      }

      return {
        success: true,
        status: result.status
      };

    } catch (error) {
      console.error('Error getting user status from backend:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Refresh user's IAP status on backend
   * @returns {Promise<Object>} Refreshed status
   */
  async refreshUserStatus() {
    try {
      if (!this.deviceId || !this.channelId) {
        await this.initializeDeviceInfo();
      }

      const response = await fetch(`${this.baseURL}/refresh-status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          deviceId: this.deviceId,
          channelId: this.channelId
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to refresh status');
      }

      return {
        success: true,
        status: result.status
      };

    } catch (error) {
      console.error('Error refreshing user status:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get user's purchase history from backend
   * @param {number} limit - Number of purchases to fetch
   * @param {number} offset - Offset for pagination
   * @returns {Promise<Object>} Purchase history
   */
  async getPurchaseHistory(limit = 10, offset = 0) {
    try {
      if (!this.deviceId || !this.channelId) {
        await this.initializeDeviceInfo();
      }

      const response = await fetch(
        `${this.baseURL}/purchases/${this.deviceId}/${this.channelId}?limit=${limit}&offset=${offset}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to get purchase history');
      }

      return {
        success: true,
        purchases: result.purchases,
        pagination: result.pagination
      };

    } catch (error) {
      console.error('Error getting purchase history:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Check if backend is healthy
   * @returns {Promise<boolean>} Health status
   */
  async checkHealth() {
    try {
      const response = await fetch(`${this.baseURL}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      return response.ok;
    } catch (error) {
      console.error('Backend health check failed:', error);
      return false;
    }
  }

  /**
   * Set custom backend URL (useful for testing)
   * @param {string} url - Backend URL
   */
  setBackendURL(url) {
    this.baseURL = url;
    console.log('Backend URL updated to:', url);
  }
}

export default new BackendIAPService();
