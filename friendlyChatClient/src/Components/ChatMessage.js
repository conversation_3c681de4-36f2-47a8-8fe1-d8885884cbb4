import React, { useContext, useEffect, useState } from 'react';
import { StyleSheet, View, Dimensions, Image, Platform, Text } from 'react-native';
import { useTheme } from 'react-native-paper';
import Colors from '../Constants/Colors';
import ImageMessage from './Messages/ImageMessage';
import TextMessage from './Messages/TextMessage';
import Profile from './Messages/Profile';
import Avatar from './Messages/Avatar';
import Time from './Messages/Time';
import Ticks from './Messages/ticks';
import Name from './Messages/Name';
import AboutUs from './Messages/AboutUs';
import MessageReactions from './Messages/MessageReactions';
import ReactionPicker from './Messages/ReactionPicker';
import { useRecoilValue } from 'recoil';
import { partnerSelector } from '../store/session/selectors';
import SettingsContext from '../contexts/SettingsContext';
import SocketService from '../services/socketIoService';

const windowWidth = Dimensions.get('window').width;
const windowHeight = Dimensions.get('window').height;
const maxHeight = (2 * windowHeight) / 5;
const maxWidth = (7 * windowWidth) / 10;
const minWidth = windowWidth / 2;
export default function ChatMessage(props) {
  const theme = useTheme();
  const context = useContext(SettingsContext);
  const [reactionPickerVisible, setReactionPickerVisible] = useState(false);
  const [reactionPickerPosition] = useState({ x: 0, y: 0 });

  // Add safety check for props.item
  if (!props.item) {
    const errorStyles = makeErrorStyles(theme);
    return <View style={errorStyles.errorContainer}><Text style={{ color: theme.colors.text }}>Message unavailable</Text></View>;
  }

  let { text, source, time, status, uri, aboutUs } = props.item;
  const isServerMessage = source === 'user';
  const isSystemMessage = source === 'sys';
  // Create dynamic styles based on theme
  const dynamicStyles = makeDynamicStyles(theme);

  // Handle reaction toggle - single function for both add and remove
  const handleReactionToggle = (messageId, reactionType) => {
    // Update local state immediately for better UX
    // For toggle, we determine the action based on current state
    const reaction = props.item.reactions === reactionType ? null : reactionType;
    console.log('handleReactionToggle', reaction, reactionType, props.item);
    if (isServerMessage) {
      updateLocalReaction(messageId, reaction);
      SocketService.toggleReaction(messageId, reaction);
    }
  };

  // Handle reaction selection from picker - now uses toggle
  const handleReactionSelect = (messageId, reactionType) => {
    // For picker, always add the reaction (toggle to add)
    updateLocalReaction(messageId, reactionType, true);
    SocketService.toggleReaction(messageId, reactionType);
  };

  // Update local reaction state immediately
  const updateLocalReaction = (messageId, reaction) => {
    if (props.onReactionUpdate) {
      props.onReactionUpdate(messageId, reaction);
    }
  };

  // Handle long press to show reaction picker
  const handleLongPress = (event) => {
    if (isSystemMessage) return;
    setReactionPickerVisible(true);
  };

  // Close reaction picker
  const closeReactionPicker = () => {
    setReactionPickerVisible(false);
  };


  let direction = isServerMessage
    ? styles.left
    : isSystemMessage
      ? styles.center
      : aboutUs
        ? styles.left
        : styles.right;
  let TextStyle = isServerMessage
    ? dynamicStyles.text
    : isSystemMessage
      ? dynamicStyles.sysText
      : dynamicStyles.userText;
  let bodyStyle = isServerMessage
    ? dynamicStyles.leftStyling
    : isSystemMessage
      ? styles.sysStyling
      : aboutUs
        ? dynamicStyles.leftStyling
        : dynamicStyles.rightStyling;
  let [image, setImage] = useState({ width: 0, height: 0 });
  const partner = useRecoilValue(partnerSelector(props.item.partner));
  useEffect(() => {
    if (props.item && props.item.uri) {
      Image.getSize(
        props.item.uri,
        (srcWidth, srcHeight) => {
          let ratio = srcWidth / srcHeight;

          if (ratio * maxHeight < maxWidth) {
            setImage({ width: ratio * maxHeight, height: maxHeight });
          } else {
            setImage({ width: maxWidth, height: maxWidth / ratio });
          }
        },
        error => {
          console.log('Image size error:', error);
          // Set default image dimensions on error
          setImage({ width: maxWidth * 0.7, height: maxHeight * 0.7 });
        },
      );
    }
  }, [props.item?.uri]); // More specific dependency
  return (
    <View
      style={
        (isServerMessage && styles.row) ||
        (!isServerMessage && !isSystemMessage && styles.rowReverse)
      }>
      {isServerMessage && partner && (
        <Profile
          id={partner._id}
          gender={partner.gender}
          avatar={partner.avatar}
        />
      )}
      {!uri && !isServerMessage && !isSystemMessage && !aboutUs && (
        <Avatar gender={context.gender} avatar={context.avatar} />
      )}
      <View style={[styles.container, direction]}>
        {isServerMessage && (
          <Name
            isSystemMessage={isSystemMessage}
            partner={partner}
            style={dynamicStyles.grayText}
          />
        )}
        {!isServerMessage && !isSystemMessage && (
          <Name
            isSystemMessage={isSystemMessage}
            partner={{ name: context.name }}
            style={dynamicStyles.grayText}
          />
        )}

        <View
          style={[styles.bodyStyle, bodyStyle, uri && [styles.imagePadding]]}>
          {text && (
            <TextMessage
              style={TextStyle}
              onDelete={props.item.delete}
              id={props.item.id}
              isServerMessage={isServerMessage}
              onLongPress={handleLongPress}>
              {text}
            </TextMessage>
          )}
          {uri && (
            <ImageMessage
              image={props.item}
              showButton={isServerMessage}
              style={{ width: image.width, height: image.height }}
              onLongPress={handleLongPress}
            />
          )}
          {aboutUs && <AboutUs onLongPress={handleLongPress} />}
        </View>

        {!isSystemMessage && (
          <View style={[styles.info, uri && { width: image.width }]}>
            {status && <Ticks status={status} style={dynamicStyles.grayText} />}
            {time && <Time date={time} style={dynamicStyles.grayText} />}
          </View>
        )}

        {/* Add MessageReactions component for non-system messages */}
        <MessageReactions
          messageId={props.item.id}
          reactions={props.item.reactions}
          onReactionPress={handleReactionToggle}
        />
      </View>

      {/* Reaction Picker */}
      {isServerMessage && (<ReactionPicker
        visible={reactionPickerVisible}
        onReactionSelect={handleReactionSelect}
        onClose={closeReactionPicker}
        position={reactionPickerPosition}
        messageId={props.item.id}
      />)}
    </View>
  );
}
const makeErrorStyles = (theme) => StyleSheet.create({
  errorContainer: {
    padding: 10,
    backgroundColor: theme.colors.surface,
    borderRadius: 5,
    margin: 5,
  },
});
const makeDynamicStyles = (theme) => StyleSheet.create({
  leftStyling: {
    backgroundColor: theme.colors.strangerChatting,
    borderTopEndRadius: 10,
  },
  rightStyling: {
    backgroundColor: theme.colors.userChatting,
    borderTopStartRadius: 10,
  },
  text: {
    fontSize: 16,
    color: theme.colors.text,
    fontFamily:
      Platform.OS === 'ios' ? 'Poppins-Regular' : 'PoppinsRegular-B2Bw',
  },
  sysText: {
    fontSize: 10,
    color: theme.colors.text,
    fontFamily:
      Platform.OS === 'ios' ? 'Poppins-Regular' : 'PoppinsRegular-B2Bw',
  },
  userText: {
    fontSize: 16,
    color: theme.colors.onPrimary || 'white',
    fontFamily:
      Platform.OS === 'ios' ? 'Poppins-Regular' : 'PoppinsRegular-B2Bw',
  },
  grayText: {
    fontSize: 10,
    color: theme.colors.textSecondary || theme.colors.onSurfaceVariant || Colors.gray,
    fontFamily:
      Platform.OS === 'ios' ? 'Poppins-Regular' : 'PoppinsRegular-B2Bw',
  },
});
const styles = StyleSheet.create({
  container: {
    maxWidth: maxWidth,
    minWidth: minWidth,
    marginHorizontal: 5,
  },
  row: {
    flexDirection: 'row',
  },
  rowReverse: {
    flexDirection: 'row-reverse',
  },
  info: {
    flexDirection: 'row-reverse',
  },
  bodyStyle: {
    borderBottomEndRadius: 10,
    borderBottomStartRadius: 10,
    padding: 5,
  },
  left: {
    alignSelf: 'flex-start',
  },
  center: {
    alignSelf: 'center',
    maxWidth: windowWidth,
    minWidth: 0,
  },
  right: {
    alignSelf: 'flex-end',
    alignItems: 'flex-end',
  },
  leftStyling: {
    backgroundColor: Colors.strangerChattingColor,
    borderTopEndRadius: 10,
  },
  sysStyling: {
    // backgroundColor: Colors.strangerChattingColor,
    // borderTopEndRadius: 10,
  },
  rightStyling: {
    backgroundColor: Colors.userChattingColor,
    borderTopStartRadius: 10,
  },
  text: {
    fontSize: 16,
    color: 'black',
    fontFamily:
      Platform.OS === 'ios' ? 'Poppins-Regular' : 'PoppinsRegular-B2Bw',
  },
  sysText: {
    fontSize: 10,
    color: 'black',
    fontFamily:
      Platform.OS === 'ios' ? 'Poppins-Regular' : 'PoppinsRegular-B2Bw',
  },
  userText: {
    fontSize: 16,
    color: 'white',
    fontFamily:
      Platform.OS === 'ios' ? 'Poppins-Regular' : 'PoppinsRegular-B2Bw',
  },
  imagePadding: { padding: 0, backgroundColor: null },
  grayText: {
    fontSize: 10,
    color: Colors.gray,
    fontFamily:
      Platform.OS === 'ios' ? 'Poppins-Regular' : 'PoppinsRegular-B2Bw',
  },
  errorContainer: {
    padding: 10,
    backgroundColor: '#f0f0f0',
    borderRadius: 5,
    margin: 5,
  },
});
