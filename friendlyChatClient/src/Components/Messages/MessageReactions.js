import React from 'react';
import { View, StyleSheet, TouchableOpacity, Text, Animated } from 'react-native';
import { useTheme } from 'react-native-paper';

// Reaction emoji mapping
const REACTION_EMOJIS = {
  like: '👍',
  heart: '❤️',
  laugh: '😂',
  wow: '😮'
};

const REACTION_ORDER = ['like', 'heart', 'laugh', 'wow'];

export default function MessageReactions({ messageId, reactions, onReactionPress, }) {
  const theme = useTheme();
  const styles = createStyles(theme);

  // Handle case where reactions is null or undefined
  if (!reactions) return null;


  const handleReactionPress = (reactionType) => {
    // For toggle approach, just pass the reaction type
    // The parent component will handle the toggling logic
    console.log('reactionType', reactionType);
    onReactionPress(messageId, reactionType);
  };
  console.log('reactions', reactions);
  const hasAnyReaction = reactions != null;
  console.log('hasAnyReaction', hasAnyReaction, reactions, REACTION_EMOJIS[reactions]);
  // Show reaction if either user or partner has reacted
  if (!hasAnyReaction) {
    return null;
  }
  return (
    <View style={styles.container}>
      <TouchableOpacity
        key={reactions}
        style={[
          styles.reactionButton,
        ]}
        onPress={() => handleReactionPress(reactions)}
        activeOpacity={0.7}
        hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
      >
        <Text style={styles.emoji}>
          {REACTION_EMOJIS[reactions]}
        </Text>
      </TouchableOpacity>
    </View>
  );
}

const createStyles = (theme) => StyleSheet.create({
  container: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 4,
    marginHorizontal: 8,
  },
  reactionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginRight: 6,
    marginBottom: 4,
    borderWidth: 1,
    borderColor: theme.colors.outline,
    minHeight: 28,
  },
  reactionButtonActive: {
    backgroundColor: theme.colors.primaryContainer,
    borderColor: theme.colors.primary,
  },
  emoji: {
    fontSize: 14,
    marginRight: 4,
  },
  count: {
    fontSize: 12,
    fontWeight: '600',
    color: theme.colors.onSurface,
    minWidth: 12,
    textAlign: 'center',
  },
  countActive: {
    color: theme.colors.primary,
  },
});
