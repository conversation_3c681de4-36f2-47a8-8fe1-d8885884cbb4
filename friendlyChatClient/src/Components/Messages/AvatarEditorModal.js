import React, { useState, useCallback } from 'react';
import {
    Modal,
    View,
    StyleSheet,
    TouchableOpacity,
    ScrollView,
    Dimensions,
    SafeAreaView,
    Alert,
} from 'react-native';
import {
    Text,
    Button,
    Portal,
    useTheme,
    Surface,
    Chip,
} from 'react-native-paper';
import { BigHead } from 'react-native-bigheads';
import {
    accessoryMap,
    clothingMap,
    eyebrowsMap,
    eyesMap,
    facialHairMap,
    hairMap,
    hatMap,
    mouthsMap,
    theme as bigHeadTheme,
    bgShapeMap,
} from 'react-native-bigheads';
import { useIsAdFree } from '../../contexts/IAPContext';

const { width, height } = Dimensions.get('window');

const CATEGORIES = [
    { key: 'accessory', label: 'Accessory', map: accessoryMap },
    { key: 'clothing', label: 'Clothing', map: clothingMap },
    { key: 'clothingColor', label: 'Clothing Color', map: bigHeadTheme.colors.clothing },
    { key: 'eyebrows', label: 'Eyebrows', map: eyebrowsMap },
    { key: 'eyes', label: 'Eyes', map: eyesMap },
    { key: 'facialHair', label: 'Facial Hair', map: facialHairMap },
    { key: 'hair', label: 'Hair', map: hairMap },
    { key: 'hairColor', label: 'Hair Color', map: bigHeadTheme.colors.hair },
    { key: 'hat', label: 'Hat', map: hatMap },
    { key: 'mouth', label: 'Mouth', map: mouthsMap },
    { key: 'skinTone', label: 'Skin Tone', map: bigHeadTheme.colors.skin },
    { key: 'bgShape', label: 'background Shape', map: bgShapeMap },
];

export default function AvatarEditorModal({ visible, onDismiss, avatar, onAvatarChange, gender, purchaseOneMonth, TryPremium }) {
    const theme = useTheme();
    const [activeCategory, setActiveCategory] = useState('accessory');
    const isAdFree = useIsAdFree();

    // Define free categories (first 3)
    const FREE_CATEGORIES = ['accessory', 'clothing', 'clothingColor'];

    const getTitle = (text) => {
        const result = text.replace(/([A-Z])/g, ' $1');
        return result.charAt(0).toUpperCase() + result.slice(1);
    };

    // Helper function to check if category is premium
    const isPremiumCategory = (categoryKey) => {
        return !FREE_CATEGORIES.includes(categoryKey);
    };

    const handleItemSelect = (category, item) => {
        if (isPremiumCategory(category) && !isAdFree) {
            Alert.alert(
                'Premium Feature',
                'Upgrade to Premium to unlock all avatar customization options and enjoy an ad-free experience!',
                [
                    { text: 'Cancel', style: 'cancel' },
                    { text: TryPremium, onPress: purchaseOneMonth }
                ]
            );
            return;
        }
        onAvatarChange({ ...avatar, [category]: item });
    };

    const randomizeAvatar = () => {
        const newAvatar = { ...avatar };

        // Always randomize free categories
        newAvatar.accessory = selectRandomKey(accessoryMap);
        newAvatar.clothing = selectRandomKey(clothingMap);
        newAvatar.clothingColor = selectRandomKey(bigHeadTheme.colors.clothing);

        // Only randomize premium categories if user is ad-free
        if (isAdFree) {
            newAvatar.eyebrows = selectRandomKey(eyebrowsMap);
            newAvatar.eyes = selectRandomKey(eyesMap);
            newAvatar.facialHair = selectRandomKey(facialHairMap);
            newAvatar.hair = selectRandomKey(hairMap);
            newAvatar.hairColor = selectRandomKey(bigHeadTheme.colors.hair);
            newAvatar.hat = selectRandomKey(hatMap);
            newAvatar.mouth = selectRandomKey(mouthsMap);
            newAvatar.skinTone = selectRandomKey(bigHeadTheme.colors.skin);
            newAvatar.bgShape = selectRandomKey(bgShapeMap);
        }

        onAvatarChange(newAvatar);
    };

    const selectRandomKey = (object) => {
        return Object.keys(object)[
            Math.floor(Math.random() * Object.keys(object).length)
        ];
    };

    const renderCategoryItems = () => {
        const category = CATEGORIES.find(cat => cat.key === activeCategory);
        if (!category) return null;
        console.log(category);

        return (
            <ScrollView
                contentContainerStyle={styles.itemsContainer}
                showsVerticalScrollIndicator={false}
                style={styles.itemsScrollView}
            >
                {Object.entries(category.map).map(([key, value]) => {
                    if (key.startsWith('none') && key.length === 5) return null;

                    const isSelected = avatar[category.key] === key;
                    const isPremium = isPremiumCategory(category.key);
                    const isLocked = isPremium && !isAdFree;

                    return (
                        <TouchableOpacity
                            key={key}
                            style={[
                                styles.itemCard,
                                isSelected && styles.selectedItemCard,
                                {
                                    backgroundColor: theme.colors.surface,
                                    borderColor: isSelected ? theme.colors.primary : theme.colors.outline,
                                    opacity: isLocked ? 0.5 : 1
                                }
                            ]}
                            onPress={() => handleItemSelect(category.key, key)}
                            activeOpacity={0.7}
                        >
                            <View style={styles.previewContainer}>
                                <BigHead
                                    bgShape="circle"
                                    {...avatar}
                                    {...{ [category.key]: key }}
                                    hatColor="white"
                                    lipColor="pink"
                                    lashes={false}
                                    graphic="react"
                                    bgColor={theme.colors.primary}
                                    body={gender === 'm' ? 'chest' : 'breasts'}
                                    size={50}
                                />
                            </View>
                            <Text
                                variant="bodySmall"
                                style={[
                                    styles.itemText,
                                    isSelected && { color: theme.colors.primary, fontWeight: '600' },
                                    { color: theme.colors.onSurface }
                                ]}
                                numberOfLines={2}
                            >
                                {getTitle(key)}
                            </Text>
                        </TouchableOpacity>
                    );
                })}
            </ScrollView>
        );
    };

    return (
        <Portal>
            <Modal
                visible={visible}
                onDismiss={onDismiss}
                animationType="slide"
            >
                <SafeAreaView style={[styles.fullScreenContainer, { backgroundColor: theme.colors.background }]}>
                    <View style={styles.modalContent}>
                        {/* Header */}
                        <View style={[styles.header, { borderBottomColor: theme.colors.outline }]}>
                            <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.onSurface }]}>
                                Customize Avatar
                            </Text>
                            <Button
                                mode="text"
                                onPress={onDismiss}
                                icon="close"
                                compact
                                contentStyle={styles.closeButtonContent}
                            />
                        </View>

                        {/* Avatar Preview */}
                        <View style={[styles.avatarPreview, { backgroundColor: theme.colors.surfaceVariant }]}>
                            <BigHead
                                bgShape="circle"
                                {...avatar}
                                hatColor="white"
                                lipColor="pink"
                                lashes={false}
                                graphic="react"
                                bgColor={theme.colors.primary}
                                body={gender === 'm' ? 'chest' : 'breasts'}
                                size={120}
                            />
                        </View>

                        {/* Category Tabs */}
                        <View style={styles.tabsSection}>
                            <ScrollView
                                horizontal
                                showsHorizontalScrollIndicator={false}
                                contentContainerStyle={styles.tabsContainer}
                                style={styles.tabsScrollView}
                            >
                                {CATEGORIES.map((category) => {
                                    const isPremium = isPremiumCategory(category.key);
                                    const isLocked = isPremium && !isAdFree;

                                    return (
                                        <Chip
                                            key={category.key}
                                            selected={activeCategory === category.key}
                                            onPress={() => setActiveCategory(category.key)}
                                            style={[
                                                styles.tabChip,
                                                {
                                                    backgroundColor: activeCategory === category.key
                                                        ? theme.colors.primary
                                                        : theme.colors.surfaceVariant,
                                                    opacity: isLocked ? 0.6 : 1
                                                }
                                            ]}
                                            textStyle={[
                                                styles.tabText,
                                                {
                                                    color: activeCategory === category.key
                                                        ? theme.colors.onPrimary
                                                        : theme.colors.onSurfaceVariant
                                                }
                                            ]}
                                            showSelectedOverlay={false}
                                            icon={isLocked ? "lock" : undefined}
                                        >
                                            {category.label}
                                        </Chip>
                                    );
                                })}
                            </ScrollView>
                        </View>

                        {/* Items Grid */}
                        <View style={[styles.itemsWrapper, { backgroundColor: theme.colors.surfaceVariant }]}>
                            {renderCategoryItems()}
                        </View>

                        {/* Randomize Button */}
                        <View style={styles.footerSection}>
                            <Button
                                mode="contained"
                                icon="shuffle-variant"
                                onPress={randomizeAvatar}
                                style={styles.randomizeButton}
                                contentStyle={styles.buttonContent}
                            >
                                Randomize Avatar
                            </Button>
                        </View>
                    </View>
                </SafeAreaView>
            </Modal>
        </Portal>
    );
}

const styles = StyleSheet.create({
    fullScreenContainer: {
        flex: 1,
    },
    modalContent: {
        flex: 1,
        paddingHorizontal: 16,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: 16,
        borderBottomWidth: 1,
    },
    title: {
        flex: 1,
        fontWeight: '700',
        fontSize: 24,
    },
    closeButtonContent: {
        paddingHorizontal: 12,
        paddingVertical: 8,
    },
    avatarPreview: {
        alignItems: 'center',
        marginVertical: 20,
        paddingVertical: 30,
        paddingHorizontal: 20,
        borderRadius: 20,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    tabsSection: {
        marginBottom: 16,
    },
    tabsScrollView: {
        flexGrow: 0,
    },
    tabsContainer: {
        paddingHorizontal: 8,
        gap: 12,
        paddingVertical: 8,
        alignItems: 'center',
    },
    tabChip: {
        marginHorizontal: 0,
        borderRadius: 24,
        height: 40,
        justifyContent: 'center',
        minWidth: 90,
        paddingHorizontal: 16,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3,
    },
    tabText: {
        fontSize: 13,
        fontWeight: '600',
        textAlign: 'center',
    },
    itemsWrapper: {
        flex: 1,
        borderRadius: 16,
        padding: 8,
        marginBottom: 16,
    },
    itemsScrollView: {
        flex: 1,
    },
    itemsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-around',
        paddingHorizontal: 8,
        paddingBottom: 16,
        paddingTop: 8,
    },
    itemCard: {
        width: (width - 64) / 3, // Adjusted for fullscreen
        aspectRatio: 0.85,
        alignItems: 'center',
        justifyContent: 'center',
        padding: 10,
        marginBottom: 16,
        borderRadius: 16,
        borderWidth: 2,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 3,
        },
        shadowOpacity: 0.12,
        shadowRadius: 4,
    },
    selectedItemCard: {
        borderWidth: 3,
        elevation: 6,
        shadowOpacity: 0.2,
        shadowRadius: 6,
        transform: [{ scale: 1.02 }],
    },
    previewContainer: {
        marginBottom: 8,
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(255,255,255,0.7)',
        borderRadius: 12,
        padding: 4,
    },
    itemText: {
        fontSize: 11,
        textAlign: 'center',
        lineHeight: 13,
        paddingHorizontal: 4,
        fontWeight: '500',
    },
    footerSection: {
        paddingVertical: 16,
        alignItems: 'center',
    },
    randomizeButton: {
        borderRadius: 28,
        minWidth: 200,
        elevation: 4,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.15,
        shadowRadius: 6,
    },
    buttonContent: {
        paddingHorizontal: 32,
        paddingVertical: 14,
    },
});