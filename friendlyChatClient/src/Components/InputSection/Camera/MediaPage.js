import React, { useCallback, useMemo, useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Image,
  ActivityIndicator,
  Alert,
} from 'react-native';
import Reanimated, {
  withTiming,
  useAnimatedStyle,
} from 'react-native-reanimated';
import { SAFE_AREA_PADDING } from '../../../Constants/Dimentions';
import { PressableOpacity } from 'react-native-pressable-opacity';
import IonIcon from 'react-native-vector-icons/Ionicons';
import ImageResizer from '@bam.tech/react-native-image-resizer';
import Marker, { Position } from 'react-native-image-marker';
import iconUri from '../../../assets/images/ic_notification.png';
import RNFS from 'react-native-fs';

export function MediaPage({ navigation, route }) {
  const { path, type = 'photo' } = route.params;
  const [hasMediaLoaded, setHasMediaLoaded] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  // Navigation event handlers
  useEffect(() => {
    const unsubBlur = navigation.addListener('blur', () => {
      setHasMediaLoaded(false);
    });

    const unsubBeforeRemove = navigation.addListener('beforeRemove', () => {
      setHasMediaLoaded(false);
      setIsProcessing(false);
    });

    return () => {
      unsubBlur();
      unsubBeforeRemove();
    };
  }, [navigation]);

  const onMediaLoadEnd = useCallback(() => {
    if (route.params?.path) {
      setHasMediaLoaded(true);
    }
  }, [route.params?.path]);
  const onSavePressed = useCallback(async () => {
    if (isProcessing) return;

    try {
      if (!route?.params?.path) throw new Error('No image path provided');
      setIsProcessing(true);

      // Check if the component is still mounted before proceeding
      const filePath = route.params.path;
      if (!(await RNFS.exists(filePath))) {
        throw new Error('Source image not found');
      }

      // Process image
      const resizedImage = await ImageResizer.createResizedImage(
        filePath,
        1920,
        1080,
        'JPEG',
        90,
        0,
        undefined,
        false,
      );

      if (!resizedImage?.uri) throw new Error('Image resize failed');
      console.log('Resized image:', resizedImage);
      let finalUri = resizedImage.uri;
      if (route.params.isCam) {
        const watermarked = await Marker.markImage({
          backgroundImage: {
            src: resizedImage.uri,
            scale: 1,
          },
          watermarkImages: [
            {
              src: iconUri,
              position: {
                position: Position.bottomRight,
              },
            },
          ],
          quality: 100,
        });
        finalUri = watermarked;
        if (!finalUri.startsWith('file://')) {
          finalUri = 'file://' + finalUri;
        }
      }
      console.log('Final image:', finalUri);

      const base64 = await RNFS.readFile(finalUri, 'base64');

      // Ensure clean navigation
      await new Promise(resolve => setTimeout(resolve, 100));
      navigation.navigate('Home', {
        img: { uri: finalUri, base64, type },
      });
    } catch (error) {
      console.error('Image processing error:', error);
      if (error.message !== 'No image path provided') {
        Alert.alert('Error', error.message);
      }
      Alert.alert('Error', error.message);
    } finally {
      setIsProcessing(false);
    }
  }, [navigation, route?.params, type]);

  const source = useMemo(() => ({ uri: path }), [path]);

  const screenStyle = useAnimatedStyle(() => {
    const initialOpacity = 0;
    const initialScale = 0.95;
    return {
      opacity: withTiming(hasMediaLoaded ? 1 : initialOpacity, {
        duration: 300,
      }),
      transform: [
        {
          scale: withTiming(hasMediaLoaded ? 1 : initialScale, {
            duration: 300,
          }),
        },
      ],
    };
  }, [hasMediaLoaded]);

  useEffect(() => {
    // Setup effect
    const setupAnimation = async () => {
      // Any setup needed
    };
    setupAnimation();

    // Cleanup effect
    return () => {
      setHasMediaLoaded(false);
      setIsProcessing(false);
    };
  }, []);

  return (
    <View style={styles.container}>
      <Reanimated.View style={[styles.imageContainer, screenStyle]}>
        {path && (
          <Image
            source={source}
            style={styles.image}
            resizeMode="contain"
            onLoadEnd={onMediaLoadEnd}
            onError={() => {
              console.error('Image failed to load');
              navigation.goBack();
            }}
          />
        )}
      </Reanimated.View>

      <PressableOpacity
        style={styles.closeButton}
        onPress={navigation.goBack}
        disabled={isProcessing}>
        <IonIcon name="close" size={35} color="white" style={styles.icon} />
      </PressableOpacity>

      <PressableOpacity
        style={styles.send}
        onPress={onSavePressed}
        disabled={isProcessing}>
        {isProcessing ? (
          <ActivityIndicator color="white" size="large" />
        ) : (
          <IonIcon name="send" size={35} color="white" style={styles.icon} />
        )}
      </PressableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  imageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  closeButton: {
    position: 'absolute',
    top: SAFE_AREA_PADDING.paddingTop,
    left: SAFE_AREA_PADDING.paddingLeft,
    width: 40,
    height: 40,
  },
  send: {
    position: 'absolute',
    bottom: SAFE_AREA_PADDING.paddingBottom,
    right: SAFE_AREA_PADDING.paddingRight,
    width: 40,
    height: 40,
  },
  icon: {
    textShadowColor: 'black',
    textShadowOffset: {
      height: 0,
      width: 0,
    },
    textShadowRadius: 1,
  },
});
